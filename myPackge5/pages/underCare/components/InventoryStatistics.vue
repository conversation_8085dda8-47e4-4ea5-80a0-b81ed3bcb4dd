<template>
  <view class="inventory-container">
    <scroll-view
      class="inventory-list"
      scroll-y="true"
      :scroll-with-animation="true"
      @scrolltolower="scrollToLower"
      :refresher-enabled="true"
      :refresher-triggered="refresherState"
      @refresherrefresh="bindrefresherrefresh"
    >
      <view class="list-content">
        <view v-for="(item, index) in list" :key="index" class="list-item">
          <view class="item-header">
            <text class="item-time">{{ item.pastureName }}</text>
            <view @click="handleDetail(item)">
              <text class="item-time oper-detail">查看详情</text>
              <u-icon name="arrow-right" color="#fff" size="22"></u-icon>
            </view>
          </view>
          <view class="item-content">
            <view class="content-row">
              <text class="content-label">牛只总数：</text>
              <text class="content-value">{{ item.livestockNum || 0 }}头</text>
            </view>
            <view class="content-row">
              <text class="content-label">牛只品种：</text>
              <text class="content-value">{{ item.varietyNum || 0 }}种</text>
            </view>
            <view class="content-row">
              <text class="content-label">平均体重：</text>
              <text class="content-value">{{ item.averageWeight || 0 }}kg</text>
            </view>
          </view>
        </view>

        <nullList v-if="isEmpty" />
        <view v-if="!noMore && list.length > 0" class="load-more">加载更多...</view>
        <view v-if="noMore && list.length > 0" class="load-more">没有更多数据了</view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import nullList from '@/components/null-list/index.vue'
import { pasturePage } from '@/api/pages/livestock/farm'

export default {
  name: 'InventoryStatistics',
  components: {
    nullList
  },
  props: {
    filterParams: {
      type: Object,
      default: () => ({})
    },
    resetSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      refresherState: false,
      noMore: false,
      isEmpty: false,
      list: [],
      pageNum: 1,
      pageSize: 20
    }
  },
  mounted() {
    this.getList()

    // 监听库存统计记录更新事件
    uni.$on('updateInventoryList', () => {
      this.pageNum = 1
      this.noMore = false
      this.getList()
    })
  },

  beforeDestroy() {
    // 移除事件监听
    uni.$off('updateInventoryList')
  },
  
  watch: {
    filterParams: {
      handler() {
        this.pageNum = 1;
        this.noMore = false;
        this.getList();
      },
      deep: true
    },
    resetSearch: {
      handler() {
        this.pageNum = 1;
        this.noMore = false;
        this.getList();
      }
    }
  },
  
  methods: {
    getList() {
      uni.showLoading({ title: '加载中', icon: 'none' })

      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        ...this.filterParams
      }

      pasturePage(params).then(res => {
        const isSuccess = res.code === 200
        const newList = isSuccess ? (res.result?.list || []) : []
        const total = isSuccess ? (res.result?.total || 0) : 0

        this.updateList(newList, total)
      }).catch(() => {
        this.$toast('获取数据失败')
        this.updateList([], 0)
      }).finally(() => {
        uni.hideLoading()
      })
    },

    updateList(newList, total) {
      if (this.pageNum >= 2) {
        this.list = this.list.concat(newList)
        this.noMore = this.list.length >= total
      } else {
        this.isEmpty = total < 1
        this.list = total >= 1 ? newList : []
        this.noMore = this.list.length >= total
      }
    },

    scrollToLower() {
      if (this.noMore) return
      this.pageNum++
      this.getList()
    },

    bindrefresherrefresh() {
      this.refresherState = true
      this.pageNum = 1
      this.noMore = false
      this.getList()
      setTimeout(() => {
        this.refresherState = false
        this.$toast('刷新成功')
      }, 1000)
    },

    handleDetail(item) {
      uni.navigateTo({
        url: '/myPackge5/pages/underCare/inventoryDetails?pastureId=' + (item.pastureId || item.id)
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/underCareList.scss';
</style>
