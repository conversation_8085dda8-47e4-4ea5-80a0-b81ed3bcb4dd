<template>
  <view class="under-care-container">
    <scroll-view
      class="under-care-list"
      scroll-y="true"
      :scroll-with-animation="true"
      @scrolltolower="scrollToLower"
      :refresher-enabled="true"
      :refresher-triggered="refresherState"
      @refresherrefresh="bindrefresherrefresh"
    >
      <view class="list-content">
        <view v-for="(item, index) in list" :key="index" class="list-item">
        <view class="item-header">
          <text class="item-time">{{ item.operateTime }}</text>
          <view @click="handleDetail(item)">
            <text class="item-time oper-detail">查看详情 </text>
          <u-icon name="arrow-right" color="#fff" size="22"></u-icon>
          </view>
        </view>
        <view class="item-content">
          <view class="content-row">
            <text class="content-label">疫苗种类：</text>
            <text class="content-value">{{ item.earTagNo }}</text>
          </view>
           <view class="content-row">
            <text class="content-label">接种对象：</text>
            <text class="content-value">{{ item.vaccineDose }}</text>
          </view>
          <view class="content-row">
            <text class="content-label">接种数量：</text>
            <text class="content-value">{{ item.vaccineDose }}</text>
          </view>
         
         <!--  <view class="content-row">
            <text class="content-label">疫苗厂家：</text>
            <text class="content-value">{{ item.company }}</text>
          </view>
          <view class="content-row" v-if="item.operatePeopleName">
            <text class="content-label">操作人：</text>
            <text class="content-value">{{ item.operatePeopleName }}</text>
          </view>
          <view class="content-row" v-if="item.operateTypeName">
            <text class="content-label">接种方式：</text>
            <text class="content-value">{{ item.operateTypeName }}</text>
          </view>
          <view class="content-row" v-if="item.remark">
            <text class="content-label">备注：</text>
            <text class="content-value remark-text">{{ item.remark }}</text>
          </view> -->
        </view>
        </view>

        <nullList v-if="isEmpty" />
        <view v-if="!noMore && list.length > 0" class="load-more">加载更多...</view>
        <view v-if="noMore && list.length > 0" class="load-more">没有更多数据了</view>
      </view>
    </scroll-view>

    <view class="fixed-add-btn" @click="addRecord">
      <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/add.png" alt="" />
    </view>


  </view>
</template>

<script>
import { diseaPage } from '@/api/pages/livestock/underCare'
import nullList from '@/components/null-list/index.vue'

export default {
  name: 'DiseaseControl',
  components: {
    nullList
  },
  props: {
    filterParams: {
      type: Object,
      default: () => ({})
    },
    resetSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      refresherState: false,
      noMore: false,
      isEmpty: false,
      list: [],
      pageNum: 1,
      pageSize: 10
    }
  },
  mounted() {
    this.getList()
    uni.$on('updateDiseaseList', () => {
      this.pageNum = 1
      this.noMore = false
      this.getList()
    })
  },

  beforeDestroy() {
    // 移除事件监听
    uni.$off('updateDiseaseList')
  },
  watch: {
    filterParams: {
      handler() {
        this.pageNum = 1;
        this.noMore = false;
        this.getList();
      },
      deep: true
    },
    resetSearch: {
      handler() {
        this.pageNum = 1;
        this.noMore = false;
        this.getList();
      }
    }
  },
  methods: {
    getList() {
    //   uni.showLoading({ title: '加载中', icon: 'none' })

      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        earTagNo: this.filterParams.earTagNo || '',
        startTime: this.filterParams.startTime || '',
        endTime: this.filterParams.endTime || ''
      }

      diseaPage(params).then(res => {
        const isSuccess = res.code === 200
        const newList = isSuccess ? (res.result?.list || []) : []
        const total = isSuccess ? (res.result?.total || 0) : 0

        this.updateList(newList, total)
      }).catch(() => {
        this.$toast('获取数据失败')
        this.updateList([], 0)
      }).finally(() => {
        uni.hideLoading()
      })
    },

    updateList(newList, total) {
      if (this.pageNum >= 2) {
        this.list = this.list.concat(newList)
        this.noMore = this.list.length >= total
      } else {
        this.isEmpty = total < 1
        this.list = total >= 1 ? newList : []
        this.noMore = this.list.length >= total
      }
    },

    scrollToLower() {
      if (this.noMore) return
      this.pageNum++
      this.getList()
    },

    bindrefresherrefresh() {
      this.refresherState = true
      this.pageNum = 1
      this.noMore = false
      this.getList()
      setTimeout(() => {
        this.refresherState = false
        this.$toast('刷新成功')
      }, 1000)
    },

      addRecord() {
          uni.navigateTo({
              url: '/myPackge5/pages/underCare/addVaccForm'
          });
      },

      handleDetail(item) {
        // 通过事件通知父组件显示详情弹窗
        // this.$emit('showVaccinationDetail', item);
      }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/underCareList.scss';
</style>
