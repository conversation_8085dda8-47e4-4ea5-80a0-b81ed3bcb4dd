<template>
    <view>
        <CustomNavbar :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
        <view class="header">
            <view class="fifter" v-if="currentTab!=1">
                <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png"
                    alt="" @click="fifterClick" />
            </view>
        </view>

        <view class="tab-container">
            <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false" scroll-with-animation="true"
                :scroll-into-view="shouldScroll ? scrollIntoView : ''">
                <view class="tabs">
                    <view class="tab-item" :class="{ 'active': currentTab === 1 }" @click="switchTab(1)" :id="'tab-1'">
                        <view class="icon-box" :class="{ 'active': currentTab === 1 }">
                            <image :src="`${obs}/nmb-mini/mine/kucunguanli.png`" class="tab-icon" />
                        </view>
                        库存统计
                        <view v-if="currentTab === 1" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 2 }" @click="switchTab(2)" :id="'tab-2'">
                        <view class="icon-box" :class="{ 'active': currentTab === 2 }">
                            <image :src="`${obs}/nmb-mini/mine/siyang.png`" class="tab-icon" />
                        </view>
                        饲养管理
                        <view v-if="currentTab === 2" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 3 }" @click="switchTab(3)" :id="'tab-3'">
                        <view class="icon-box" :class="{ 'active': currentTab === 3 }">
                            <image :src="`${obs}/nmb-mini/mine/shengzhang.png`" class="tab-icon" />
                        </view>
                        生长监测
                        <view v-if="currentTab === 3" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 4 }" @click="switchTab(4)" :id="'tab-4'">
                        <view class="icon-box" :class="{ 'active': currentTab === 4 }">
                            <image :src="`${obs}/nmb-mini/mine/jeizhong.png`" class="tab-icon" />
                        </view>
                        接种记录
                        <view v-if="currentTab === 4" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 5 }" @click="switchTab(5)" :id="'tab-5'">
                        <view class="icon-box" :class="{ 'active': currentTab === 5 }">
                            <image :src="`${obs}/nmb-mini/mine/jibing.png`" class="tab-icon" />
                        </view>
                        疾病防控
                        <view v-if="currentTab === 5" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 6 }" @click="switchTab(6)" :id="'tab-6'">
                        <view class="icon-box" :class="{ 'active': currentTab === 6 }">
                            <image :src="`${obs}/nmb-mini/mine/richang.png`" class="tab-icon" />
                        </view>
                        日常记录
                        <view v-if="currentTab === 6" class="bubble-arrow"></view>
                    </view>
                </view>
            </scroll-view>
        </view>

        <scroll-view
            class="tab-content"
            scroll-y="true"
            :scroll-with-animation="true"
            @scrolltolower="scrollToLower"
            :refresher-enabled="true"
            :refresher-triggered="refresherState"
            @refresherrefresh="bindrefresherrefresh"
            :refresher-threshold="45"
            :refresher-default-style="'black'"
            :refresher-background="'transparent'"
            :enable-back-to-top="true"
        >
            <view class="list-content">
                <view v-for="(item, index) in currentList" :key="index" class="list-item">
                    <!-- 库存统计 -->
                    <template v-if="currentTab === 1">
                        <view class="item-header">
                            <text class="item-time">{{ item.pastureName }}</text>
                            <view @click="handleInventoryDetail(item)">
                                <text class="item-time oper-detail">查看详情</text>
                                <u-icon name="arrow-right" color="#fff" size="22"></u-icon>
                            </view>
                        </view>
                        <view class="item-content">
                            <view class="content-row">
                                <text class="content-label">牛只总数：</text>
                                <text class="content-value">{{ item.livestockNum || 0 }}头</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">牛只品种：</text>
                                <text class="content-value">{{ item.varietyNum || 0 }}种</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">健康牛只：</text>
                                <text class="content-value">{{ item.healthyNum || 0 }}头</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">异常牛只：</text>
                                <text class="content-value">{{ item.abnormalNum || 0 }}头</text>
                            </view>
                        </view>
                    </template>

                    <!-- 饲养管理 -->
                    <template v-if="currentTab === 2">
                        <view class="item-header">
                            <text class="item-time">{{ item.updateTime }}</text>
                        </view>
                        <view class="item-content">
                            <view class="content-row">
                                <text class="content-label">投喂圈舍：</text>
                                <text class="content-value">{{ item.pastureName || 'xxxx' }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">饲料种类：</text>
                                <text class="content-value">{{ getFeedFoodText(item.feedFood) }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">投喂数量：</text>
                                <text class="content-value">{{ item.feedNum }}Kg</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">投喂频次：</text>
                                <text class="content-value">{{ getFeedFrequencyText(item.feedFrequency) }}</text>
                            </view>
                            <view class="content-row" v-if="item.manageRemark">
                                <text class="content-label">备注：</text>
                                <text class="content-value remark-text">{{ item.manageRemark }}</text>
                            </view>
                        </view>
                    </template>

                    <!-- 生长监测 -->
                    <template v-if="currentTab === 3">
                        <view class="item-header">
                            <text class="item-time">{{ item.operateTime }}</text>
                        </view>
                        <view class="item-content">
                            <view class="content-row">
                                <text class="content-label">耳标号：</text>
                                <text class="content-value">{{ item.earTagNo }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">体重：</text>
                                <text class="content-value">{{ item.weight }}Kg</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">体长：</text>
                                <text class="content-value">{{ item.bodyLength }}cm</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">胸围：</text>
                                <text class="content-value">{{ item.chestGirth }}cm</text>
                            </view>
                            <view class="content-row" v-if="item.remark">
                                <text class="content-label">备注：</text>
                                <text class="content-value remark-text">{{ item.remark }}</text>
                            </view>
                        </view>
                    </template>

                    <!-- 接种记录 -->
                    <template v-if="currentTab === 4">
                        <view class="item-header">
                            <text class="item-time">{{ item.operateTime }}</text>
                            <view @click="showVaccinationDetail(item)">
                                <text class="item-time oper-detail">查看详情 </text>
                                <u-icon name="arrow-right" color="#fff" size="22"></u-icon>
                            </view>
                        </view>
                        <view class="item-content">
                            <view class="content-row">
                                <text class="content-label">耳标号：</text>
                                <text class="content-value">{{ item.earTagNo }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">接种对象：</text>
                                <text class="content-value">{{ item.vaccineDose }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">疫苗名称：</text>
                                <text class="content-value">{{ item.vaccineName }}</text>
                            </view>
                        </view>
                    </template>

                    <!-- 疾病防控 -->
                    <template v-if="currentTab === 5">
                        <view class="item-header">
                            <text class="item-time">{{ item.operateTime }}</text>
                            <view @click="showDiseaseDetail(item)">
                                <text class="item-time oper-detail">查看详情 </text>
                                <u-icon name="arrow-right" color="#fff" size="22"></u-icon>
                            </view>
                        </view>
                        <view class="item-content">
                            <view class="content-row">
                                <text class="content-label">耳标号：</text>
                                <text class="content-value">{{ item.earTagNo }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">接种对象：</text>
                                <text class="content-value">{{ item.vaccineDose }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">疫苗名称：</text>
                                <text class="content-value">{{ item.vaccineName }}</text>
                            </view>
                        </view>
                    </template>

                    <!-- 日常记录 -->
                    <template v-if="currentTab === 6">
                        <view class="item-header">
                            <text class="item-time">{{ item.operateTime }}</text>
                        </view>
                        <view class="item-content">
                            <view class="content-row">
                                <text class="content-label">耳标号：</text>
                                <text class="content-value">{{ item.earTagNo }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">记录类型：</text>
                                <text class="content-value">{{ getDailyRecordTypeText(item.recordType) }}</text>
                            </view>
                            <view class="content-row">
                                <text class="content-label">记录内容：</text>
                                <text class="content-value">{{ item.recordContent }}</text>
                            </view>
                            <view class="content-row" v-if="item.remark">
                                <text class="content-label">备注：</text>
                                <text class="content-value remark-text">{{ item.remark }}</text>
                            </view>
                        </view>
                    </template>
                </view>

                <nullList v-if="isEmpty" />
                <view v-if="!noMore && currentList.length > 0" class="load-more">加载更多...</view>
                <view v-if="noMore && currentList.length > 0" class="load-more">没有更多数据了</view>
            </view>
        </scroll-view>

        <view class="fixed-add-btn" @click="addRecord" v-if="currentTab !== 1">
            <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/add.png" alt="" />
        </view>

        <filterPopup @resetSearch="resetSearch" :filterType="filterType" :pickerFilterShow="pickerFilterShow"
            @canel="pickerFilterShow = false" @submitForm="submitForm" />

        <VaccinationDetailModal
          :visible="showVaccinationDetailModal"
          :detailData="vaccinationDetailData"
          @close="closeVaccinationDetailModal"
        />

        <VaccinationDetailModal
          :visible="showDiseaseDetailModal"
          :detailData="diseaseDetailData"
          @close="closeDiseaseDetailModal"
        />
    </view>
</template>

<script>
import filterPopup from './components/filterPopup.vue'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'
import VaccinationDetailModal from './components/VaccinationDetailModal.vue'
import nullList from '@/components/null-list/index.vue'
import { inventoryPage } from '@/api/pages/livestock/underCare'
import { feedPage } from '@/api/pages/livestock/underCare'
import { growthPage } from '@/api/pages/livestock/underCare'
import { vaccinePage } from '@/api/pages/livestock/underCare'
import { diseaPage } from '@/api/pages/livestock/underCare'
import { dailyPage } from '@/api/pages/livestock/underCare'
import { getDicts } from '@/api/dict.js'
const app = getApp();

export default {
    components: {
        CustomNavbar,
        filterPopup,
        VaccinationDetailModal,
        nullList
    },
    data() {
        return {
            obs: app.globalData.obs,
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            pickerFilterShow: false,
            filters: {},
            currentTab: 1, // 当前选中的tab
            scrollIntoView: '', // 滚动到指定元素
            shouldScroll: false, // 是否需要滚动的标志
            totalTabs: 6, // 总tab数量
            visibleTabCount: 3, // 可视区域完全显示的tab数量
            currentVisibleStart: 1, // 当前可视区域起始位置
            filterType: 'inventory',
            filterParams: {}, // 筛选参数
            resetSearchFlag: false, // 重置搜索标志

            // 列表数据相关
            currentList: [],
            isEmpty: false,
            noMore: false,
            pageNum: 1,
            pageSize: 10,
            refresherState: false,

            // 疫苗详情弹窗相关
            showVaccinationDetailModal: false,
            vaccinationDetailData: null,

            // 疾病防控详情弹窗相关
            showDiseaseDetailModal: false,
            diseaseDetailData: null,

            // 字典数据
            feedFoodDict: [],
            feedFrequencyDict: [],
            dailyRecordTypeDict: []
        }
    },
    onLoad() {
        this.loadDictData();
        this.getList();
    },
    onUnload() {
    },
    onShow() { },
    computed: {

    },
    methods: {
        // 加载字典数据
        async loadDictData() {
            try {
                const [feedFoodRes, feedFrequencyRes, dailyRecordTypeRes] = await Promise.all([
                    getDicts('feed_food'),
                    getDicts('feed_frequency'),
                    getDicts('daily_record_type')
                ]);
                this.feedFoodDict = feedFoodRes.result || [];
                this.feedFrequencyDict = feedFrequencyRes.result || [];
                this.dailyRecordTypeDict = dailyRecordTypeRes.result || [];
            } catch (error) {
                console.error('加载字典数据失败:', error);
            }
        },

        // 获取列表数据
        getList() {
            const apiMap = {
                1: inventoryPage,
                2: feedPage,
                3: growthPage,
                4: vaccinePage,
                5: diseaPage,
                6: dailyPage
            };

            const api = apiMap[this.currentTab];
            if (!api) return;

            const params = {
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                earTagNo: this.filterParams.earTagNo || '',
                startTime: this.filterParams.startTime || '',
                endTime: this.filterParams.endTime || ''
            };

            api(params).then(res => {
                const isSuccess = res.code === 200;
                const newList = isSuccess ? (res.result?.list || []) : [];
                const total = isSuccess ? (res.result?.total || 0) : 0;

                this.updateList(newList, total);
            }).catch(() => {
                this.$toast('获取数据失败');
                this.updateList([], 0);
            });
        },

        // 更新列表数据
        updateList(newList, total) {
            if (this.pageNum >= 2) {
                this.currentList = this.currentList.concat(newList);
                this.noMore = this.currentList.length >= total;
            } else {
                this.isEmpty = total < 1;
                this.currentList = total >= 1 ? newList : [];
                this.noMore = this.currentList.length >= total;
            }
        },

        // 滚动到底部
        scrollToLower() {
            if (this.noMore) return;
            this.pageNum++;
            this.getList();
        },

        // 下拉刷新
        bindrefresherrefresh() {
            this.refresherState = true;
            this.pageNum = 1;
            this.noMore = false;
            this.getList();
            setTimeout(() => {
                this.refresherState = false;
                this.$toast('刷新成功');
            }, 1000);
        },

        switchTab(tabIndex) {
            // 如果点击的是当前已激活的tab，直接返回
            if (tabIndex === this.currentTab) {
                console.log('点击当前已激活的tab，无需处理');
                return;
            }

            const typeMap = { 1: 'inventory', 2: 'breeding', 3: 'growth', 4: 'vaccination', 5: 'disease', 6: 'daily' };

            // 切换tab时清空筛选条件和列表数据
            this.filterParams = {};
            this.resetSearchFlag = !this.resetSearchFlag;
            this.currentTab = tabIndex;
            this.filterType = typeMap[tabIndex];

            // 重置列表状态
            this.pageNum = 1;
            this.noMore = false;
            this.currentList = [];
            this.isEmpty = false;

            // 获取新的列表数据
            this.getList();

            this.$nextTick(() => {
                const targetScrollTab = this.calculateTargetScrollTab(tabIndex);
                if (targetScrollTab !== null) {
                    // 需要滚动
                    console.log(`需要滚动到tab-${targetScrollTab}`);
                    this.shouldScroll = true;
                    this.scrollIntoView = `tab-${targetScrollTab}`;
                    // 更新当前可视区域起始位置
                    this.updateCurrentVisibleStart(targetScrollTab);

                    setTimeout(() => {
                        this.shouldScroll = false;
                        this.scrollIntoView = '';
                    }, 300);
                } else {
                    // 不需要滚动
                    console.log('不需要滚动，保持当前状态');
                    this.shouldScroll = false;
                    this.scrollIntoView = '';
                }
            });
        },



        calculateTargetScrollTab(targetTab) {
            if (this.totalTabs <= this.visibleTabCount) {
                console.log('总tab数不超过可视数量，无需滚动');
                return null;
            }

            // 获取当前可视区域
            const currentVisibleStart = this.getCurrentVisibleStart();
            const currentVisibleEnd = currentVisibleStart + this.visibleTabCount - 1;


            let scrollToTab = null;

            const isInVisibleArea = targetTab >= currentVisibleStart && targetTab <= currentVisibleEnd;
            const isOutsideLeft = targetTab < currentVisibleStart;
            const isOutsideRight = targetTab > currentVisibleEnd;

            if (isInVisibleArea) {
                const isAdjacentToActive = Math.abs(targetTab - this.currentTab) === 1;

                if (isAdjacentToActive) {
                    // 点击相邻tab，不滚动
                    console.log('点击相邻tab，不滚动');
                    return null;
                } else {
                    // 点击间隔tab，需要滚动
                    if (targetTab > this.currentTab) {
                        // 向右滑动：让目标tab成为可视区域第二个
                        scrollToTab = targetTab - this.visibleTabCount + 2;
                    } else {
                        // 向左滑动：让目标tab成为可视区域第二个
                        scrollToTab = targetTab - 1;
                    }
                }
            } else if (isOutsideLeft) {
                // 点击左侧外部tab，向右滑动，让目标tab成为第二个
                scrollToTab = targetTab - 1;
            } else if (isOutsideRight) {
                scrollToTab = targetTab - this.visibleTabCount + 2;
            }

            // 确保滚动目标在有效范围内
            if (scrollToTab !== null) {
                scrollToTab = Math.max(1, Math.min(scrollToTab, this.totalTabs - this.visibleTabCount + 1));
            } else {
            }

            return scrollToTab;
        },

        getCurrentVisibleStart() {
            if (!this.currentVisibleStart) {
                this.currentVisibleStart = 1; // 初始状态从第一个tab开始
            }
            return this.currentVisibleStart;
        },

        // 更新当前可视区域起始位置
        updateCurrentVisibleStart(newStart) {
            this.currentVisibleStart = newStart;
        },

        // 动态设置tab数量 
        setTotalTabs(count) {
            this.totalTabs = count;
            // 重置可视区域到开始位置
            this.currentVisibleStart = 1;
        },

        // 动态设置可视tab数量 
        setVisibleTabCount(count) {
            this.visibleTabCount = count;
            // 重新计算当前可视区域
            this.currentVisibleStart = Math.min(this.currentVisibleStart, this.totalTabs - count + 1);
        },
        // 搜索
        fifterClick() {
            this.pickerFilterShow = true
        },

        resetSearch() {
            this.filterParams = {};
            this.resetSearchFlag = !this.resetSearchFlag;
            this.pageNum = 1;
            this.noMore = false;
            this.getList();
        },
        submitForm(val) {
            this.filterParams = { ...val };
            this.pickerFilterShow = false;
            this.pageNum = 1;
            this.noMore = false;
            this.getList();
        },

        // 疫苗详情弹窗相关方法
        showVaccinationDetail(data) {
            this.vaccinationDetailData = data;
            this.showVaccinationDetailModal = true;
        },

        closeVaccinationDetailModal() {
            this.showVaccinationDetailModal = false;
            this.vaccinationDetailData = null;
        },

        // 疾病防控详情弹窗相关方法
        showDiseaseDetail(data) {
            this.diseaseDetailData = data;
            this.showDiseaseDetailModal = true;
        },

        closeDiseaseDetailModal() {
            this.showDiseaseDetailModal = false;
            this.diseaseDetailData = null;
        },

        // 字典转换方法
        getFeedFoodText(value) {
            const item = this.feedFoodDict.find(item => item.dictValue === value);
            return item ? item.dictLabel : value;
        },

        getFeedFrequencyText(value) {
            const item = this.feedFrequencyDict.find(item => item.dictValue === value);
            return item ? item.dictLabel : value;
        },

        getDailyRecordTypeText(value) {
            const item = this.dailyRecordTypeDict.find(item => item.dictValue === value);
            return item ? item.dictLabel : value;
        },

        // 库存详情处理
        handleInventoryDetail(item) {
            uni.navigateTo({
                url: '/myPackge5/pages/underCare/inventoryDetails?pastureId=' + (item.pastureId || item.id)
            });
        },

        // 添加记录方法
        addRecord() {
            const urlMap = {
                2: '/myPackge5/pages/underCare/addFeedForm',
                3: '/myPackge5/pages/underCare/addGrowForm',
                4: '/myPackge5/pages/underCare/addVaccForm',
                5: '/myPackge5/pages/underCare/addDiseaForm',
                6: '/myPackge5/pages/underCare/addDailyForm'
            };

            const url = urlMap[this.currentTab];
            if (url) {
                uni.navigateTo({ url });
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.header {
    width: 750rpx;
    height: 727rpx;
    display: flex;
    padding-top: 120rpx;
    /* 导航栏空间 */
    box-sizing: border-box;
    position: relative;
    background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/bg.png) no-repeat 100% 100%;
    background-size: 100% 100%;
    position: relative;
}

.fifter {
    position: absolute;
    top: 195rpx;
    right: 30rpx;

    img {
        width: 34rpx;
        height: 32.5rpx;
    }
}

.tab-container {
    margin-top: -372rpx;
    padding: 0 26rpx;
    // margin-bottom: 30rpx;
    overflow: visible;
}

.tabs-scroll {
    width: 100%;
    white-space: nowrap;
    overflow: visible;
    padding-bottom: 15rpx;
}

.tabs {
    display: flex;
    align-items: center;
    width: max-content;
    padding: 0 10rpx;
    overflow: visible;
    height: 100rpx;
}

.tab-item {
    width: 188rpx;
    height: 65rpx;
    background: #FFFFFF;
    border-radius: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1DB17A;
    font-weight: 500;
    font-size: 26rpx;
    position: relative;
    transition: all 0.3s ease;
    margin-right: 17rpx;
    flex-shrink: 0;
    padding: 5rpx 20rpx 5rpx 5rpx;

    .icon-box {
        width: 50rpx;
        height: 50rpx;
        background-color: #E3FFEE;
        border-radius: 50%;
        margin-right: 10rpx;
        padding: 12rpx;
        box-sizing: border-box;

        &.active {
            background-color: #ffffff !important;
        }

        .tab-icon {
            width: 25rpx;
            height: 25rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    &:last-child {
        margin-right: 10rpx;
    }

    &.active {
        color: #FFFFFF;
        background: linear-gradient(140deg, #1CC271 0%, #5CD26F 100%);
    }
}

.bubble-arrow {
    position: absolute;
    bottom: -10rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10rpx solid transparent;
    border-right: 10rpx solid transparent;
    border-top: 10rpx solid #5CD26F;
    z-index: 999;
}

.tab-content {
    padding: 0 30rpx;
    height: calc(100vh - 500rpx);
}

.list-content {
    padding-bottom: 40rpx;
}

.list-item {
    background: #ffffff;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    margin-bottom: 30rpx;
}

.item-header {
    height: 56rpx;
    background: linear-gradient(260deg, #60D26F 0%, #1CC271 100%);
    line-height: 56rpx;
    padding: 0 26rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #FFFFFF;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-time {
    flex: 1;
}

.oper-detail {
    text-align: right;
    font-size: 24rpx;
    margin-right: 10rpx;
}

.item-content {
    padding: 25rpx;
    background: #FFFFFF;
}

.content-row {
    display: flex;
    margin-bottom: 16rpx;
    font-size: 26rpx;
    line-height: 38rpx;

    &:last-child {
        margin-bottom: 0;
    }
}

.content-label {
    color: #999999;
    font-weight: 400;
}

.content-value {
    color: #333333;
    font-weight: 400;
    flex: 1;
}

.remark-text {
    word-break: break-all;
}

.fixed-add-btn {
    position: fixed;
    right: 10rpx;
    bottom: 290rpx;
    width: 130rpx;
    height: 122rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;

    &:active {
        transform: scale(0.95);
        transition: transform 0.1s;
    }

    img {
        width: 100%;
        height: 100%;
    }
}

.load-more {
    text-align: center;
    padding: 30rpx;
    color: #999999;
    font-size: 24rpx;
}

.main {
    margin-top: -372rpx;
}

.Add {
    width: 152rpx;
    height: 145rpx;
    position: absolute;
    bottom: 290rpx;
    right: 10rpx;

    img {
        width: 152rpx;
        height: 145rpx;
    }
}
</style>
