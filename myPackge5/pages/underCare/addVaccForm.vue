<template>
    <view>
        <CustomNavbar :title="pageTitle" :titleColor="'##333333'" />
        <view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }">
            <view class="container base-container">
                <view class="section">
                    <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto"
                        :label-style="labelStyle">
                        <u-form-item label="免疫日期：" required prop="operateTime" class="operateTime">
                            <text :class="form.operateTime ? 'common' : 'tips'" @click="!isDetail && showDatePicker()"
                                style="text-align: right; font-size: 26rpx;">
                                {{ form.operateTime || "请选择" }}
                            </text>
                        </u-form-item>
                        <u-form-item label="疫苗名称：" required prop="vaccineName">
                            <u-input v-model="form.vaccineName" placeholder="请输入" :custom-style="customStyle"
                                :placeholder-style="placeholderStyle" :disabled="isDetail" />
                        </u-form-item>
                        <u-form-item label="接种方式：" required prop="operateType" :border-bottom="false"
                            class="operateType">
                            <view class="cultivate-section">
                                <view class="cultivate-options">
                                    <view v-for="item in vaccinationList" :key="item.value"
                                        :class="['cultivate-item', { 'active': form.operateType === item.value }]"
                                        @click="!isDetail && selectOperateType(item.value)">
                                        {{ item.label }}
                                    </view>
                                </view>
                            </view>
                        </u-form-item>
                        <u-form-item label="疫苗厂家：" prop="company">
                            <u-input v-model="form.company" placeholder="选填，请输入" :custom-style="customStyle"
                                :placeholder-style="placeholderStyle" :disabled="isDetail" />
                        </u-form-item>
                        <u-form-item label="疫苗批次：" prop="vaccineBatches" :border-bottom="false">
                            <u-input v-model="form.vaccineBatches" placeholder="选填，请输入" :custom-style="customStyle"
                                :placeholder-style="placeholderStyle" :disabled="isDetail" />
                        </u-form-item>
                        <u-form-item label="接种说明：" prop="vaccineBatches" :border-bottom="false">
                            <u-input v-model="form.remark" placeholder="选填，接种说明，200字以内" type="textarea"
                             maxlength="200" :disabled="isDetail" />
                        </u-form-item>
                       
                    </u-form>
                </view>
            </view>
            <view class="section-title">接种耳标号</view>
            <view class="container base-container tagbox">
                <view class="immune-table">
                    <view class="table-header">
                        <view class="header-cell sequence">序号</view>
                        <view class="header-cell ear-tag">耳标号</view>
                        <view class="header-cell dosage">剂量（ml）</view>
                        <view class="header-cell operation">操作</view>
                    </view>

                    <view class="table-body">
                        <view v-for="(item, index) in immuneList" :key="index" class="table-row">
                            <view class="table-cell sequence">{{ index + 1 }}</view>
                            <view class="table-cell ear-tag">
                                <u-input v-model="item.earTagNo" placeholder="耳标编号"
                                    :custom-style="tableInputStyle" :placeholder-style="tablePlaceholderStyle"
                                    :disabled="isDetail" :border="false" @blur="searchLivestockByEarTag(item)" />
                            </view>
                            <view class="table-cell dosage">
                                <u-input v-model="item.vaccineDose" placeholder="剂量"
                                    :custom-style="tableInputStyle" :placeholder-style="tablePlaceholderStyle"
                                    :disabled="isDetail" :border="false" />
                            </view>
                            <view class="table-cell operation">
                                <view v-if="!isDetail" class="delete-btn" @click="removeImmuneItem(index)">
                                    <text class="delete-text">删除</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                    <view v-if="!isDetail" class="add-row-btn" @click="addImmuneItem">
                        <text class="add-text">新增 +</text>
                    </view>
            </view>
        </view>

        <view v-if="!isDetail" class="bg-box">
            <view class="add-btn" @click="submitForm">提交</view>
        </view>

        <u-calendar range-bg-color="rgba(64,202,143,0.13)" active-bg-color="#40CA8F" range-color="#40CA8F"
            btn-type="success" v-model="showData" :mode="`date`" @change='changeData' :max-date='maxdata'
            :min-date="mindata"></u-calendar>
    </view>
</template>

<script>
import { selectEarTagNo, addpatch } from '@/api/pages/livestock/underCare'
import CustomNavbar from '../components/CustomNavbar.vue'
import { getDicts } from "@/api/dict.js"

export default {
    name: 'addDiseaForm',
    components: {
        CustomNavbar
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            livestockManageId: '',
            isDetail: false,
            isSubmitting: false,
            form: {
                vaccineName: '',        // 疫苗名称
                operateType: '',        // 接种方式
                company: '',            // 疫苗厂家
                vaccineBatches: '',     // 疫苗批次
                userId: '',             // 牧民Id
                remark: '',             // 免疫说明
                operateTime: ''         // 操作日期
            },
            immuneList: [
                {
                    earTagNo: '',       // 耳标编号
                    vaccineDose: '',    // 接种剂量
                    livestockId: ''     // 活畜ID
                }
            ],
            errorType: ['message'],
            customStyle: { textAlign: 'right', fontSize: '26rpx' },
            labelStyle: { color: '#333', fontSize: '26rpx' },
            placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
            tableInputStyle: { textAlign: 'center', fontSize: '26rpx', padding: '0' },
            tablePlaceholderStyle: 'text-align:center;color:#999;font-size: 26rpx;',
            mindata: "1995-09-09",
            maxdata: '2095-09-09',
            showData: false,
            livestockInfo: null,
            vaccinationList: [],
            rules: {
                operateTime: [{
                    required: true,
                    message: '请选择免疫日期',
                    trigger: ['blur', 'change']
                }],
                vaccineName: [{
                    required: true,
                    message: '请输入疫苗名称',
                    trigger: ['blur', 'change']
                }],
                operateType: [{
                    required: true,
                    message: '请选择接种方式',
                    trigger: ['blur', 'change']
                }],
            }
        }
    },

    computed: {
        navbarTotalHeight() {
            return (this.systemInfo.statusBarHeight || 0) + 44;
        },
        pageTitle() {
            return this.isDetail ? '免疫接种' : '新增接种';
        },
        isFormValid() {
            const { operateTime, vaccineName, operateType } = this.form;
            const hasValidImmuneList = this.immuneList.length > 0 &&
                this.immuneList.every(item => item.earTagNo && item.vaccineDose);
            return operateTime && vaccineName && operateType && hasValidImmuneList;
        },
    },

    onLoad(options) {
        this.livestockManageId = options.livestockManageId || '';
        this.isDetail = !!options.livestockManageId;
        this.loadVaccinationDict();

        // if (this.livestockManageId) {
        //     this.getDetail();
        // }
    },

    onReady() {
        this.$refs.uForm?.setRules?.(this.rules);
        this.$nextTick(() => {
            this.$refs.uForm2?.setRules?.({});
        });
    },

    methods: {
        async loadVaccinationDict() {
            try {
                const res = await getDicts('livestock_vaccination_way');
                if (res && res.data) {
                    this.vaccinationList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载接种方式字典失败:', error);
            }
        },

        selectOperateType(value) {
            this.form.operateType = value;
            this.resetField('operateType');
        },

        // 添加免疫项
        addImmuneItem() {
            // 检查是否有未完成
            const hasIncompleteRow = this.immuneList.some(item =>
                !item.earTagNo.trim() || !item.vaccineDose.trim() || !item.livestockId.trim()
            );

            if (hasIncompleteRow) {
                this.$toast('请先完成当前行的输入');
                return;
            }

            this.immuneList.push({
                earTagNo: '',
                vaccineDose: '',
                livestockId: ''
            });
        },

        // 删除免疫项
        removeImmuneItem(index) {
            if (this.immuneList.length > 1) {
                this.immuneList.splice(index, 1);
            } else {
                this.$toast('至少保留一个耳标项');
            }
        },

        // 根据耳标号查询活畜信息
        async searchLivestockByEarTag(item) {
            if (!item.earTagNo.trim()) {
                // 如果耳标号为空，清空对应的livestockId
                item.livestockId = '';
                return;
            }
            try {
                const res = await selectEarTagNo({ earTagNo: item.earTagNo });
                if (res.code === 200 && res.result) {
                    item.livestockId = res.result.livestockId;
                    item.pastureId = res.result.pastureId;

                    if (!this.form.userId) {
                        this.form.userId = res.result.userId;
                    }
                } else {
                    item.livestockId = '';
                    this.$toast(`耳标号 ${item.earTagNo} 未找到对应的活畜信息`);
                }
            } catch (error) {
                console.error('查询活畜信息失败:', error);
                item.livestockId = '';
                this.$toast('查询活畜信息失败');
            }
        },

        showDatePicker() {
            this.showData = true;
        },

        changeData(e) {
            this.form.operateTime = e.result;
            this.showData = false;
            this.resetField('operateTime');
        },

        async submitForm() {
            if (this.isSubmitting) return;

            this.isSubmitting = true;
            try {
                const valid = await this.validateForm();
                if (!valid) {
                    return;
                }

                const submitData = {
                    vaccineName: this.form.vaccineName,
                    operateType: this.form.operateType,
                    company: this.form.company,
                    vaccineBatches: this.form.vaccineBatches,
                    userId: this.form.userId || '',
                    earTagNo: this.immuneList.length > 0 ? this.immuneList[0].earTagNo : '', 
                    livestockId: 1,
                    remark: this.form.remark,
                    operateTime: this.form.operateTime,
                    immuneList: this.immuneList.map(item => ({
                        earTagNo: item.earTagNo,
                        vaccineDose: item.vaccineDose,
                        livestockId: item.livestockId,
                        pastureId: item.pastureId
                    }))
                };

                const res = await addpatch(submitData);

                if (res.code === 200) {
                    uni.$emit('updateVaccineList');
                    this.$toast('接种记录添加成功');
                    uni.navigateBack({ delta: 1 });
                } else {
                    throw new Error(res.message || '提交失败');
                }
            } catch (error) {
                this.handleError(error, '提交失败');
            } finally {
                this.isSubmitting = false;
            }
        },

        validateForm() {
            return new Promise(resolve => {
                const { operateTime, vaccineName, operateType } = this.form;

                if (!operateTime) {
                    this.$toast('请选择免疫日期');
                    resolve(false);
                    return;
                }
                if (!vaccineName) {
                    this.$toast('请输入疫苗名称');
                    resolve(false);
                    return;
                }
                if (!operateType) {
                    this.$toast('请选择接种方式');
                    resolve(false);
                    return;
                }

                // 验证免疫列表
                if (this.immuneList.length === 0) {
                    this.$toast('请至少添加一个耳标号');
                    resolve(false);
                    return;
                }

                for (let i = 0; i < this.immuneList.length; i++) {
                    const item = this.immuneList[i];
                    if (!item.earTagNo) {
                        this.$toast(`请输入第${i + 1}个耳标编号`);
                        resolve(false);
                        return;
                    }
                    if (!item.vaccineDose) {
                        this.$toast(`请输入第${i + 1}个耳标的接种剂量`);
                        resolve(false);
                        return;
                    }
                    if (!item.livestockId) {
                        this.$toast(`第${i + 1}个耳标编号对应的活畜信息未找到，请重新输入`);
                        resolve(false);
                        return;
                    }
                }

                resolve(true);
            });
        },

        handleError(error, customMessage = '') {
            console.error(error);
            this.$toast(error.message || customMessage || '操作失败');
        },

        resetField(prop) {
            let field = this.$refs.uForm?.fields?.find(field => field.prop === prop);
            if (field) {
                field.resetField();
                return;
            }
            field = this.$refs.uForm2?.fields?.find(field => field.prop === prop);
            if (field) {
                field.resetField();
            }
        }
    },
}
</script>

<style lang="less" scoped>
@import url('../../css/index.less');

.common {
    color: #333;
}

.tips {
    color: #999;
}

.section {
    margin-bottom: 20rpx;

    /deep/ .u-input__textarea {
        padding: 35rpx 0 !important;
        min-height: 0 !important;
    }
}

.base-container {
    padding: 0 30rpx;
}
.tagbox{
    padding-top: 30rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    padding: 30rpx 0 0 0;
    margin: 0 30rpx;
}

.section-type {
    padding: 0;
    margin: 0 0 0 15rpx;
    font-size: 14px;
    font-weight: normal !important;
}

.second-section-title {
    padding-top: 0;
}

.operateTime {
    /deep/ .u-form-item--right__content__slot {
        text-align: right !important;
    }
}

.cultivate-section {
    margin: 0;
    background: transparent;

    .cultivate-options {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;
        margin-top: 10rpx;

        .cultivate-item {
            padding: 16rpx 40rpx;
            border-radius: 50rpx;
            font-size: 26rpx;
            color: #666;
            background-color: #F5F5F5;
            border: 2rpx solid transparent;
            transition: all 0.3s;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            &.active {
                color: #1DB17A;
                background-color: #fff;
                border-color: #1DB17A;
            }

            &.disabled {
                cursor: not-allowed;
                opacity: 0.6;
            }
        }
    }
}

.operateType {
    /deep/ .u-form-item__body {
        display: block !important;
    }
}

.immune-table {
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    border: 2rpx solid #e9ecef;

    .table-header {
        display: flex;
        // background: #f8f9fa;
        border-bottom: 2rpx solid #e9ecef;

        .header-cell {
            padding: 30rpx 20rpx;
            font-size: 28rpx;
            font-weight: 500;
            color: #666;
            text-align: center;
            border-right: 2rpx solid #e9ecef;

            &:last-child {
                border-right: none;
            }

            &.sequence {
                flex: 0 0 120rpx;
            }

            &.ear-tag {
                flex: 1;
            }

            &.dosage {
                flex: 0 0 200rpx;
            }

            &.operation {
                flex: 0 0 120rpx;
            }
        }
    }

    .table-body {
        .table-row {
            display: flex;
            border-bottom: 2rpx solid #f0f0f0;

            &:last-child {
                border-bottom: none;
            }

            .table-cell {
                padding: 20rpx;
                border-right: 2rpx solid #f0f0f0;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 80rpx;

                &:last-child {
                    border-right: none;
                }

                &.sequence {
                    flex: 0 0 120rpx;
                    font-size: 26rpx;
                    color: #333;
                }

                &.ear-tag {
                    flex: 1;
                    padding: 10rpx;
                }

                &.dosage {
                    flex: 0 0 200rpx;
                    padding: 10rpx;
                }

                &.operation {
                    flex: 0 0 120rpx;
                }

                .delete-btn {
                    .delete-text {
                        color: #ff4757;
                        font-size: 26rpx;
                    }
                }
            }
        }
    }

    
}
.add-row-btn {
        padding: 30rpx 0;
        text-align: center;

        .add-text {
            color: #1DB17A;
            font-size: 28rpx;
        }
    }
</style>
